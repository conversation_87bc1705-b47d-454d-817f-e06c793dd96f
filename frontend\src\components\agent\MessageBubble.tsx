import React from 'react';
import ReactMarkdown from 'react-markdown';

interface CodeBlock {
  language: string;
  code: string;
}

interface MessageBubbleProps {
  role: 'user' | 'assistant';
  content: string;
  codeBlocks?: CodeBlock[];
  timestamp?: Date;
  onExecuteCode?: (code: string) => void;
}

export function MessageBubble({ 
  role, 
  content, 
  codeBlocks = [], 
  timestamp,
  onExecuteCode 
}: MessageBubbleProps) {
  const isUser = role === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-[80%] ${isUser ? 'order-2' : 'order-1'}`}>
        {/* Avatar */}
        <div className={`flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
            isUser ? 'bg-[var(--excel-green)]' : 'bg-[var(--excel-blue)]'
          }`}>
            {isUser ? 'U' : 'AI'}
          </div>
          
          <div className={`flex-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {/* Message bubble */}
            <div className={`inline-block px-4 py-3 rounded-lg ${
              isUser 
                ? 'bg-[var(--excel-green)] text-white' 
                : 'bg-white border border-[var(--excel-border)] text-[var(--excel-text-primary)]'
            }`}>
              {isUser ? (
                <p className="whitespace-pre-wrap">{content}</p>
              ) : (
                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown
                    components={{
                      code: ({ node, inline, className, children, ...props }) => {
                        if (inline) {
                          return (
                            <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" {...props}>
                              {children}
                            </code>
                          );
                        }
                        return (
                          <pre className="bg-gray-50 p-3 rounded-md overflow-x-auto">
                            <code className="text-sm font-mono" {...props}>
                              {children}
                            </code>
                          </pre>
                        );
                      },
                      p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                      ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                      ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                      li: ({ children }) => <li className="mb-1">{children}</li>,
                      h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                      h2: ({ children }) => <h2 className="text-md font-bold mb-2">{children}</h2>,
                      h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
                    }}
                  >
                    {content}
                  </ReactMarkdown>
                </div>
              )}
            </div>
            
            {/* Code blocks */}
            {codeBlocks.length > 0 && (
              <div className="mt-3 space-y-2">
                {codeBlocks.map((block, index) => (
                  <div key={index} className="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden">
                    <div className="flex items-center justify-between px-3 py-2 bg-gray-100 border-b border-gray-200">
                      <span className="text-xs font-medium text-gray-600 uppercase">
                        {block.language || 'Code'}
                      </span>
                      {block.language === 'python' && onExecuteCode && (
                        <button
                          onClick={() => onExecuteCode(block.code)}
                          className="text-xs px-2 py-1 bg-[var(--excel-green)] text-white rounded hover:bg-[#1a5e38] transition-colors"
                        >
                          Execute
                        </button>
                      )}
                    </div>
                    <pre className="p-3 overflow-x-auto">
                      <code className="text-sm font-mono text-gray-800">
                        {block.code}
                      </code>
                    </pre>
                  </div>
                ))}
              </div>
            )}
            
            {/* Timestamp */}
            {timestamp && (
              <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
                {timestamp.toLocaleTimeString()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
